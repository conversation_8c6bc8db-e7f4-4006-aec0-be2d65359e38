<template>
    <div class="main-operation-area-container">
        <div class="title">
            {{ mainOperationArea_Title }}
        </div>
        <div class="operation">
            <!-- 用户信息 -->
            <div v-if="currentUser" class="user-info">
                <span class="user-name">{{ currentUser.name }}</span>
                <span class="user-role">({{ getRoleText(currentUser.role) }})</span>
            </div>

            <Button class="download-button" label="下载" icon="pi pi-download" style="font-size: 1rem;"/>
            <Button class="logout-button" label="登出" icon="pi pi-sign-out"
                style="font-size: 1rem;" @click="handleLogout" severity="secondary"/>
        </div>
        
    </div>
</template>

<script setup>
import { inject } from 'vue';

import Button from 'primevue/button';
import 'primeicons/primeicons.css'

const mainOperationArea_Title = inject('mainOperationArea_Title');
const currentUser = inject('currentUser');
const handleLogout = inject('handleLogout');

// 角色文本映射
const getRoleText = (role) => {
    const roleMap = {
        'admin': '管理员',
        'user': '普通用户',
        'demo': '演示用户'
    };
    return roleMap[role] || role;
};
</script>

<style>
@layer reset, primevue, custom;

@layer custom {
   .main-operation-area-container {
        display: flex;
        flex-direction: row;  
        height: 5rem;
        /* background-color: #f2f2f2; */
        width: 100%;
   }
   .main-operation-area-container .title {
        display: flex;
        align-items: center;
        
        font-size: 1.5rem;
        font-weight: bold;
        letter-spacing: 0.04rem;
        color: #334155;

        width: 20%;

        /* background-color: rgb(180, 136, 78); */
    }
    .main-operation-area-container .operation {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 1rem;

        width: 80%;

        padding-right: 1rem;

        /* background-color: aqua; */

    }

    .user-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        margin-right: 1rem;
    }

    .user-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
    }

    .user-role {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .logout-button {
        margin-left: 0.5rem;
    }

    .main-operation-area-container .download-button {
        background-color: #202020;
        border: none;
    }

}
</style>